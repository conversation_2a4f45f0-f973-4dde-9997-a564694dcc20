# 创建接口库（包含实现文件）
add_library(plugin_interface SHARED
    src/plugin_base.cpp
)

# 设置包含目录
target_include_directories(plugin_interface
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 设置编译选项和输出目录
set_target_properties(plugin_interface PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/libs
)

# 链接依赖库 - interface 依赖 core
target_link_libraries(plugin_interface
    PUBLIC
        core
)

