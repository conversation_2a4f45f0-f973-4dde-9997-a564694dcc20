/**
 * @file plugin_base.cpp
 * @brief Implementation of PluginBase class
 * <AUTHOR> project
 * @date 2024
 */

#include "zexuan/plugin/plugin_base.hpp"
#include <stdexcept>
#include <iostream>

namespace zexuan {
namespace plugin {

PluginBase::PluginBase(std::shared_ptr<base::Mediator> mediator, 
                       int pluginId, 
                       const std::string& description)
    : base::BaseObserver(pluginId, description)
    , base::BaseSubject(pluginId, description)
    , mediator_(mediator)
    , initialized_(false)
    , registered_(false) {
    
    // Validate input parameters
    if (!mediator) {
        throw std::invalid_argument("Mediator cannot be null");
    }
    
    if (pluginId < 0) {
        throw std::invalid_argument("Plugin ID must be non-negative");
    }
    
    if (description.empty()) {
        throw std::invalid_argument("Plugin description cannot be empty");
    }
    
    // Set the mediator for the subject functionality
    setMediator(mediator_);
    
    // Register with mediator
    if (!registerWithMediator()) {
        throw std::runtime_error("Failed to register plugin with mediator");
    }
    
    std::cout << "Plugin created: ID=" << pluginId 
              << ", Description=" << description << std::endl;
}

PluginBase::~PluginBase() {
    // Perform shutdown if not already done
    if (initialized_) {
        shutdown();
    }
    
    // Unregister from mediator
    unregisterFromMediator();
    
    std::cout << "Plugin destroyed: ID=" << base::BaseObserver::getId()
              << ", Description=" << base::BaseObserver::getDescription() << std::endl;
}

void PluginBase::onNotify(base::Subject* subject, const base::Message& message) {
    // Call the base observer implementation first (handles callback if set)
    base::BaseObserver::onNotify(subject, message);
    
    // Then call our plugin-specific message processing
    processMessage(message);
}

int PluginBase::sendPluginMessage(const base::Message& message) {
    if (!mediator_) {
        std::cerr << "Plugin " << base::BaseObserver::getId() << ": No mediator available for sending message" << std::endl;
        return -1;
    }
    
    // Use the subject's sendMessage method which handles source address setting
    return sendMessage(message);
}

bool PluginBase::registerWithMediator() {
    if (!mediator_) {
        return false;
    }
    
    std::string errorMsg;
    bool success = mediator_->registerObserver(base::BaseObserver::getId(), this, errorMsg);

    if (success) {
        registered_ = true;
        std::cout << "Plugin " << base::BaseObserver::getId() << " registered with mediator successfully" << std::endl;
    } else {
        std::cerr << "Failed to register plugin " << base::BaseObserver::getId() << " with mediator: " << errorMsg << std::endl;
    }
    
    return success;
}

void PluginBase::unregisterFromMediator() {
    if (!mediator_ || !registered_) {
        return;
    }
    
    std::string errorMsg;
    bool success = mediator_->unregisterObserver(base::BaseObserver::getId(), errorMsg);

    if (success) {
        registered_ = false;
        std::cout << "Plugin " << base::BaseObserver::getId() << " unregistered from mediator successfully" << std::endl;
    } else {
        std::cerr << "Failed to unregister plugin " << base::BaseObserver::getId() << " from mediator: " << errorMsg << std::endl;
    }
}

} // namespace plugin
} // namespace zexuan
