#pragma once

#include <string>
#include <map>

#ifdef _WIN32
    #include <windows.h>
    typedef HMODULE LibraryHandle;
#else
    #include <dlfcn.h>
    typedef void* LibraryHandle;
#endif

namespace zexuan {
namespace base {

/**
 * @brief 简化的动态库加载器
 * 专注于插件系统的核心需求：加载库、获取函数指针、卸载库
 */
class DynamicLibraryLoader {
public:
    /**
     * @brief 构造函数
     */
    DynamicLibraryLoader() = default;

    /**
     * @brief 析构函数 - 自动卸载所有已加载的动态库
     */
    ~DynamicLibraryLoader();

    /**
     * @brief 加载动态库
     * @param libPath 动态库路径（完整路径，包含扩展名）
     * @return 加载成功返回true，失败返回false
     */
    bool loadLibrary(const std::string& libPath);

    /**
     * @brief 获取函数指针
     * @tparam FuncType 函数指针类型
     * @param libPath 动态库路径
     * @param functionName 函数名称
     * @return 类型安全的函数指针，失败返回nullptr
     */
    template<typename FuncType>
    FuncType getFunction(const std::string& libPath, const std::string& functionName) {
        auto it = loadedLibraries_.find(libPath);
        if (it == loadedLibraries_.end()) {
            lastError_ = "Library not loaded: " + libPath;
            return nullptr;
        }

        LibraryHandle handle = it->second;
        void* funcAddr = nullptr;

#ifdef _WIN32
        funcAddr = GetProcAddress(handle, functionName.c_str());
#else
        funcAddr = dlsym(handle, functionName.c_str());
#endif

        if (!funcAddr) {
            lastError_ = "Function not found: " + functionName + " in " + libPath;
            return nullptr;
        }

        lastError_.clear();
        return reinterpret_cast<FuncType>(funcAddr);
    }

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    const std::string& getLastError() const { return lastError_; }

    /**
     * @brief 手动卸载所有已加载的动态库
     * 在析构函数中会自动调用，但也可以手动调用提前清理
     */
    void unloadAllLibraries();

private:
    // 已加载的动态库句柄映射
    std::map<std::string, LibraryHandle> loadedLibraries_;

    // 最后的错误信息
    mutable std::string lastError_;
};

} // namespace base
} // namespace zexuan
