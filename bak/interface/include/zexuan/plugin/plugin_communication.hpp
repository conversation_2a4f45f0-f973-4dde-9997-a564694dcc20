#pragma once

/**
 * 插件系统统一API - 基于新的Observer/Subject/Mediator模式重写
 * 整合了插件管理和通信功能
 * 提供简洁统一的API接口
 */

#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/base/observer.hpp"
#include "zexuan/base/subject.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/plugin_manager.hpp"
#include "message_mediator.hpp"
#include "plugin_base.hpp"

namespace zexuan {
namespace plugin {

/**
 * 插件系统统一API - 基于新的Observer/Subject/Mediator模式
 * 提供插件管理和通信功能
 */
namespace api {

// === 插件管理API ===

/**
 * 初始化插件系统
 */
inline bool initialize() {
    return getSingleton<SimplePluginManager>()->initialize();
}

/**
 * 启动插件系统
 */
inline bool start() {
    return getSingleton<SimplePluginManager>()->start();
}

/**
 * 停止插件系统
 */
inline bool stop() {
    return getSingleton<SimplePluginManager>()->stop();
}

/**
 * 关闭插件系统
 */
inline bool shutdown() {
    return getSingleton<SimplePluginManager>()->shutdownAllPlugins();
}

/**
 * 加载插件库
 */
inline bool loadPlugin(const std::string& libPath) {
    return getSingleton<SimplePluginManager>()->loadPluginLibrary(libPath);
}

/**
 * 获取活跃插件
 */
inline std::vector<IPlugin*> getActivePlugins() {
    return getSingleton<SimplePluginManager>()->getActivePlugins();
}

/**
 * 获取插件通过名称
 */
inline IPlugin* getPlugin(const std::string& name) {
    return getSingleton<SimplePluginManager>()->getPlugin(name);
}

/**
 * 获取插件状态
 */
inline PluginState getPluginState(const std::string& pluginName) {
    return getSingleton<SimplePluginManager>()->getPluginState(pluginName);
}

/**
 * 获取管理器状态
 */
inline PluginManagerState getManagerState() {
    return getSingleton<SimplePluginManager>()->getManagerState();
}

/**
 * 获取所有插件信息
 */
inline std::vector<PluginInfo> getAllPluginInfos() {
    return getSingleton<SimplePluginManager>()->getAllPluginInfos();
}

/**
 * 获取最后的错误信息
 */
inline std::string getLastError() {
    return getSingleton<SimplePluginManager>()->getLastError();
}

/**
 * 获取已注册的插件名称列表
 */
inline std::vector<std::string> getPluginNames() {
    auto plugins = getActivePlugins();
    std::vector<std::string> names;
    for (const auto& plugin : plugins) {
        if (plugin) {
            names.push_back(plugin->getName());
        }
    }
    return names;
}

// === 新的Observer/Subject/Mediator通信API ===

/**
 * 发送消息到指定Observer
 * @param observerId 目标Observer ID
 * @param message 消息内容
 * @param sourceId 源ID
 * @return 发送结果描述
 */
inline std::string sendMessageToObserver(int observerId, const std::string& message, int sourceId = 0) {
    auto mediator = getSingleton<MessageMediator>();
    zexuan::base::Message msg(1, message, sourceId);
    std::string description;
    int result = mediator->sendMessageToObserver(observerId, msg, description, sourceId);
    return description;
}

/**
 * 发送消息到指定Subject
 * @param subjectId 目标Subject ID
 * @param message 消息内容
 * @param sourceId 源ID
 * @return 发送结果描述
 */
inline std::string sendMessageToSubject(int subjectId, const std::string& message, int sourceId = 0) {
    auto mediator = getSingleton<MessageMediator>();
    zexuan::base::Message msg(1, message, sourceId);
    std::string description;
    int result = mediator->sendMessageToSubject(subjectId, msg, description, sourceId);
    return description;
}

/**
 * 发送事件到所有关心的Observer
 * @param eventType 事件类型
 * @param deviceId 设备ID
 * @param eventData 事件数据
 * @param sourceId 源ID
 * @return 发送结果描述
 */
inline std::string sendEventToObservers(int eventType, const std::string& deviceId, const std::string& eventData, int sourceId = 0) {
    auto mediator = getSingleton<MessageMediator>();
    zexuan::base::EventMessage event(eventType, deviceId, eventData, sourceId);
    std::string description;
    int result = mediator->sendEventToObservers(event, description, sourceId);
    return description;
}

/**
 * 便捷方法：发送简单消息（向后兼容）
 * @param message 消息内容
 * @param fromPlugin 源插件名称
 * @param toPlugin 目标插件名称（空表示广播）
 */
inline void publishMessage(const std::string& message, const std::string& fromPlugin = "System", const std::string& toPlugin = "") {
    if (toPlugin.empty()) {
        // 广播消息 - 发送事件给所有Observer
        sendEventToObservers(1, "broadcast", message, 0);
    } else {
        // 点对点消息 - 通过插件名称查找正确的Observer ID
        try {
            auto plugin = getPlugin(toPlugin);
            if (plugin && plugin->asObserver()) {
                // 获取插件的Observer ID
                int targetId = plugin->asObserver()->getId();
                std::cout << "🔍 发送消息给插件 " << toPlugin << "，Observer ID: " << targetId << std::endl;
                sendMessageToObserver(targetId, message, 0);
            } else {
                std::cout << "❌ 未找到插件: " << toPlugin << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "❌ 发送消息时出错: " << e.what() << std::endl;
        }
    }
}

// === 系统状态查询API ===

/**
 * 获取Observer数量
 */
inline size_t getObserverCount() {
    return getSingleton<MessageMediator>()->getObserverCount();
}

/**
 * 获取Subject数量
 */
inline size_t getSubjectCount() {
    return getSingleton<MessageMediator>()->getSubjectCount();
}

/**
 * 获取已注册的插件名称列表（通过MessageMediator）
 */
inline std::vector<std::string> getRegisteredPluginNames() {
    return getSingleton<MessageMediator>()->getRegisteredPluginNames();
}

/**
 * 获取通信统计信息
 */
inline MessageMediator::CommunicationStats getCommunicationStats() {
    return getSingleton<MessageMediator>()->getStats();
}

/**
 * 获取消息中介者实例（用于插件基类）
 */
inline MessageMediator& getMessageMediator() {
    return *getSingleton<MessageMediator>();
}

} // namespace api

// === 向后兼容的别名 ===
namespace communication = api;  // 兼容旧的命名空间
namespace system = api;         // 兼容simple_plugin_system.hpp

// 直接导出常用函数到plugin命名空间
using api::initialize;
using api::shutdown;
using api::loadPlugin;
using api::getActivePlugins;
using api::getPlugin;
using api::getPluginNames;
using api::sendMessageToObserver;     // 新的消息发送接口
using api::sendMessageToSubject;      // 新的消息发送接口
using api::sendEventToObservers;      // 新的事件发送接口
using api::publishMessage;            // 兼容性消息接口
using api::getObserverCount;          // 系统状态查询
using api::getSubjectCount;           // 系统状态查询
using api::getRegisteredPluginNames;  // 插件名称查询
using api::getCommunicationStats;     // 通信统计信息
using api::getMessageMediator;        // 中介者访问

} // namespace plugin
} // namespace zexuan
