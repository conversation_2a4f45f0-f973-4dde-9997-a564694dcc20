#pragma once

#include "zexuan/base/mediator.hpp"
#include "zexuan/base/singleton.hpp"
#include <string>
#include <vector>
#include <memory>

namespace zexuan {
namespace plugin {

/**
 * 插件消息中介者 - 基于简化的BaseMediator实现
 * 使用新的Message类和简化的路由机制
 */
class MessageMediator : public zexuan::base::BaseMediator, public singleton<MessageMediator> {
public:
    /**
     * 获取插件通信统计信息
     */
    struct CommunicationStats {
        size_t observerCount;
        size_t messagesSent;
        size_t messagesReceived;
        size_t routingErrors;
    };

    CommunicationStats getStats() const {
        return {
            getObserverCount(),
            messagesSent_,
            messagesReceived_,
            routingErrors_
        };
    }

    /**
     * 重写消息发送方法以添加统计和日志
     */
    int sendMessage(const zexuan::base::Message& message, std::string& description) override {
        messagesReceived_++;

        // 记录消息路由信息
        uint8_t source = message.getSource();
        uint8_t target = message.getTarget();

        // 调用基类方法进行实际路由
        int result = zexuan::base::BaseMediator::sendMessage(message, description);

        if (result == 0) {
            messagesSent_++;
            // 可选：记录成功的消息传递
            // std::cout << "Message routed from " << (int)source << " to " << (int)target << std::endl;
        } else {
            routingErrors_++;
            // 记录路由错误
            std::cerr << "Failed to route message from " << (int)source
                      << " to " << (int)target << ": " << description << std::endl;
        }

        return result;
    }

    /**
     * 获取已注册的插件名称列表
     */
    std::vector<std::string> getRegisteredPluginNames() const {
        std::vector<std::string> names;
        std::lock_guard<std::mutex> lock(observersMutex_);

        for (const auto& pair : observers_) {
            if (pair.second) {
                names.push_back(pair.second->getDescription());
            }
        }
        return names;
    }



    /**
     * 重置统计信息
     */
    void resetStats() {
        messagesSent_ = 0;
        messagesReceived_ = 0;
        routingErrors_ = 0;
    }

private:
    friend class singleton<MessageMediator>;

    MessageMediator() : messagesSent_(0), messagesReceived_(0), routingErrors_(0) {
        // 自动初始化
        initialize();
    }

    ~MessageMediator() = default;

    // 统计信息
    mutable size_t messagesSent_ = 0;
    mutable size_t messagesReceived_ = 0;
    mutable size_t routingErrors_ = 0;
};

} // namespace plugin
} // namespace zexuan
