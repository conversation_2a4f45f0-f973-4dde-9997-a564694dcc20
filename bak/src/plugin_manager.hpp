#pragma once
#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/plugin/message_mediator.hpp"
#include "zexuan/base/singleton.hpp"
#include "zexuan/base/dynamic_library_loader.hpp"
#include <map>
#include <vector>
#include <string>
#include <memory>
#include <iostream>
#include <stdexcept>

namespace zexuan {
namespace plugin {

// 插件管理器状态枚举
enum class PluginManagerState {
    UNINITIALIZED,  // 未初始化
    INITIALIZED,    // 已初始化
    STARTING,       // 启动中
    RUNNING,        // 运行中
    STOPPING,       // 停止中
    STOPPED,        // 已停止
    ERROR           // 错误状态
};

// 插件状态枚举
enum class PluginState {
    UNLOADED,       // 未加载
    LOADED,         // 已加载
    INITIALIZED,    // 已初始化
    RUNNING,        // 运行中
    STOPPED,        // 已停止
    ERROR           // 错误状态
};

// 插件信息结构
struct PluginInfo {
    std::string name;
    std::string version;
    std::string description;
    std::string libPath;
    PluginState state;
    IPlugin* instance;  // 使用原始指针，生命周期由动态库管理

    PluginInfo() : state(PluginState::UNLOADED), instance(nullptr) {}
};

// 函数指针类型定义
typedef IPlugin* (*create_plugin_func)();
typedef const char* (*get_info_func)();
typedef void (*set_singleton_accessor_func)(zexuan::SingletonRegistryAccessor);
typedef void (*destroy_plugin_func)(IPlugin*);

/**
 * 增强的插件管理器
 * 参考 NXLoadNodeMgrLib 设计模式，提供完善的插件生命周期管理
 * 负责动态库加载、插件实例管理和事件通信
 */
class SimplePluginManager : public singleton<SimplePluginManager> {
public:
    /**
     * 初始化插件管理器
     * @param reserve 保留参数，用于扩展
     * @return 初始化成功返回true
     */
    bool initialize(int reserve = 0) {
        if (state_ == PluginManagerState::INITIALIZED ||
            state_ == PluginManagerState::RUNNING) {
            return true;
        }

        try {
            state_ = PluginManagerState::INITIALIZED;
            lastError_.clear();

            recordLog("插件管理器初始化成功", LogLevel::INFO);
            return true;
        } catch (const std::exception& e) {
            lastError_ = "插件管理器初始化失败: " + std::string(e.what());
            state_ = PluginManagerState::ERROR;
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }
    }

    /**
     * 启动插件管理器
     * @return 启动成功返回true
     */
    bool start() {
        if (state_ != PluginManagerState::INITIALIZED) {
            lastError_ = "插件管理器未初始化，无法启动";
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }

        try {
            state_ = PluginManagerState::STARTING;

            // 启动所有已加载的插件
            bool allStarted = true;
            for (auto& pair : pluginInfos_) {
                if (pair.second.state == PluginState::INITIALIZED) {
                    if (!startOnePlugin(pair.first)) {
                        allStarted = false;
                    }
                }
            }

            state_ = PluginManagerState::RUNNING;
            recordLog("插件管理器启动完成", LogLevel::INFO);
            return allStarted;
        } catch (const std::exception& e) {
            lastError_ = "插件管理器启动失败: " + std::string(e.what());
            state_ = PluginManagerState::ERROR;
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }
    }

    /**
     * 停止插件管理器
     * @return 停止成功返回true
     */
    bool stop() {
        if (state_ != PluginManagerState::RUNNING) {
            return true;
        }

        try {
            state_ = PluginManagerState::STOPPING;

            // 停止所有运行中的插件
            bool allStopped = true;
            for (auto& pair : pluginInfos_) {
                if (pair.second.state == PluginState::RUNNING) {
                    if (!stopOnePlugin(pair.first)) {
                        allStopped = false;
                    }
                }
            }

            state_ = PluginManagerState::STOPPED;
            recordLog("插件管理器停止完成", LogLevel::INFO);
            return allStopped;
        } catch (const std::exception& e) {
            lastError_ = "插件管理器停止失败: " + std::string(e.what());
            state_ = PluginManagerState::ERROR;
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }
    }
    /**
     * 加载插件动态库
     * @param libPath 动态库路径
     * @return 加载成功返回true
     */
    bool loadPluginLibrary(const std::string& libPath) {
        try {
            // 检查是否已经加载
            for (const auto& pair : pluginInfos_) {
                if (pair.second.libPath == libPath) {
                    lastError_ = "插件库已加载: " + libPath;
                    recordLog(lastError_, LogLevel::WARNING);
                    return true;
                }
            }

            // 使用动态库加载器加载库
            if (!libraryLoader_.loadLibrary(libPath)) {
                lastError_ = "动态库加载失败: " + libraryLoader_.getLastError();
                recordLog(lastError_, LogLevel::ERROR);
                return false;
            }

            // 获取并验证所有必需的函数指针
            PluginFunctions functions;
            if (!loadPluginFunctions(libPath, functions)) {
                libraryLoader_.unloadLibrary(libPath);
                return false;
            }

            // 设置单例访问器
            if (functions.set_accessor_func) {
                try {
                    functions.set_accessor_func([]() -> SingletonRegistry& {
                        return SingletonRegistry::getInstance();
                    });
                    recordLog("已为插件设置单例访问器: " + libPath, LogLevel::INFO);
                } catch (const std::exception& e) {
                    lastError_ = "设置单例访问器失败: " + std::string(e.what());
                    recordLog(lastError_, LogLevel::ERROR);
                    libraryLoader_.unloadLibrary(libPath);
                    return false;
                }
            } else {
                recordLog("警告: 插件未导出set_singleton_accessor函数: " + libPath, LogLevel::WARNING);
            }

            // 创建插件实例
            IPlugin* plugin = nullptr;
            try {
                plugin = functions.create_func();
                if (!plugin) {
                    lastError_ = "插件创建函数返回空指针: " + libPath;
                    recordLog(lastError_, LogLevel::ERROR);
                    libraryLoader_.unloadLibrary(libPath);
                    return false;
                }
            } catch (const std::exception& e) {
                lastError_ = "插件创建失败: " + std::string(e.what());
                recordLog(lastError_, LogLevel::ERROR);
                libraryLoader_.unloadLibrary(libPath);
                return false;
            }

            // 创建插件信息
            PluginInfo pluginInfo;
            pluginInfo.name = plugin->getName();
            pluginInfo.version = plugin->getVersion();
            pluginInfo.description = plugin->getDescription();
            pluginInfo.libPath = libPath;
            pluginInfo.state = PluginState::LOADED;
            pluginInfo.instance = plugin;

            // 获取插件详细信息
            if (functions.info_func) {
                try {
                    std::string info = functions.info_func();
                    recordLog("插件信息: " + info, LogLevel::INFO);
                } catch (const std::exception& e) {
                    recordLog("获取插件信息失败: " + std::string(e.what()), LogLevel::WARNING);
                }
            }

            // 保存插件信息
            pluginInfos_[pluginInfo.name] = pluginInfo;
            pluginFunctions_[pluginInfo.name] = functions;

            // 初始化插件
            if (!initializeOnePlugin(pluginInfo.name)) {
                pluginInfos_.erase(pluginInfo.name);
                pluginFunctions_.erase(pluginInfo.name);
                libraryLoader_.unloadLibrary(libPath);
                return false;
            }

            recordLog("插件 " + pluginInfo.name + " 加载成功", LogLevel::INFO);
            return true;

        } catch (const std::exception& e) {
            lastError_ = "加载插件时发生异常: " + std::string(e.what());
            recordLog(lastError_, LogLevel::ERROR);
            libraryLoader_.unloadLibrary(libPath);
            return false;
        }
    }
    
    /**
     * 卸载插件动态库
     * @param libPath 动态库路径
     * @return 卸载成功返回true
     */
    bool unloadPluginLibrary(const std::string& libPath) {
        try {
            // 查找对应的插件
            std::string pluginName;
            for (const auto& pair : pluginInfos_) {
                if (pair.second.libPath == libPath) {
                    pluginName = pair.first;
                    break;
                }
            }

            if (pluginName.empty()) {
                lastError_ = "未找到对应的插件: " + libPath;
                recordLog(lastError_, LogLevel::WARNING);
                return false;
            }

            return unloadOnePlugin(pluginName);

        } catch (const std::exception& e) {
            lastError_ = "卸载插件时发生异常: " + std::string(e.what());
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }
    }

    /**
     * 卸载指定插件
     * @param pluginName 插件名称
     * @return 卸载成功返回true
     */
    bool unloadOnePlugin(const std::string& pluginName) {
        try {
            auto it = pluginInfos_.find(pluginName);
            if (it == pluginInfos_.end()) {
                lastError_ = "插件不存在: " + pluginName;
                recordLog(lastError_, LogLevel::WARNING);
                return false;
            }

            // 先停止插件
            if (it->second.state == PluginState::RUNNING) {
                if (!stopOnePlugin(pluginName)) {
                    recordLog("停止插件失败，但继续卸载: " + pluginName, LogLevel::WARNING);
                }
            }

            // 销毁插件实例
            if (it->second.instance) {
                try {
                    it->second.instance->shutdown();

                    // 如果有销毁函数，使用它
                    auto funcIt = pluginFunctions_.find(pluginName);
                    if (funcIt != pluginFunctions_.end() && funcIt->second.destroy_func) {
                        funcIt->second.destroy_func(it->second.instance);
                    }
                    // 清空指针
                    it->second.instance = nullptr;
                } catch (const std::exception& e) {
                    recordLog("销毁插件实例时发生异常: " + std::string(e.what()), LogLevel::WARNING);
                }
            }

            // 卸载动态库
            std::string libPath = it->second.libPath;
            if (!libraryLoader_.unloadLibrary(libPath)) {
                lastError_ = "卸载动态库失败: " + libraryLoader_.getLastError();
                recordLog(lastError_, LogLevel::ERROR);
            }

            // 清理记录
            pluginInfos_.erase(it);
            pluginFunctions_.erase(pluginName);

            recordLog("插件 " + pluginName + " 卸载成功", LogLevel::INFO);
            return true;

        } catch (const std::exception& e) {
            lastError_ = "卸载插件时发生异常: " + std::string(e.what());
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }
    }
    
    /**
     * 获取所有活跃的插件
     * @return 活跃插件列表
     */
    std::vector<IPlugin*> getActivePlugins() const {
        std::vector<IPlugin*> plugins;
        for (const auto& pair : pluginInfos_) {
            if (pair.second.state == PluginState::RUNNING && pair.second.instance) {
                plugins.push_back(pair.second.instance);
            }
        }
        return plugins;
    }

    /**
     * 获取所有插件信息
     * @return 插件信息列表
     */
    std::vector<PluginInfo> getAllPluginInfos() const {
        std::vector<PluginInfo> infos;
        for (const auto& pair : pluginInfos_) {
            infos.push_back(pair.second);
        }
        return infos;
    }

    /**
     * 获取插件状态
     * @param pluginName 插件名称
     * @return 插件状态
     */
    PluginState getPluginState(const std::string& pluginName) const {
        auto it = pluginInfos_.find(pluginName);
        return (it != pluginInfos_.end()) ? it->second.state : PluginState::UNLOADED;
    }

    /**
     * 获取管理器状态
     * @return 管理器状态
     */
    PluginManagerState getManagerState() const {
        return state_;
    }

    /**
     * 获取最后的错误信息
     * @return 错误信息字符串
     */
    std::string getLastError() const {
        return lastError_;
    }
    
    /**
     * 关闭所有插件
     * @return 关闭成功返回true
     */
    bool shutdownAllPlugins() {
        try {
            bool allShutdown = true;
            for (auto& pair : pluginInfos_) {
                if (pair.second.state == PluginState::RUNNING) {
                    if (!stopOnePlugin(pair.first)) {
                        allShutdown = false;
                    }
                }
            }
            recordLog("所有插件关闭完成", LogLevel::INFO);
            return allShutdown;
        } catch (const std::exception& e) {
            lastError_ = "关闭所有插件时发生异常: " + std::string(e.what());
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }
    }

    /**
     * 卸载所有动态库
     * @return 卸载成功返回true
     */
    bool unloadAllLibraries() {
        try {
            bool allUnloaded = true;

            // 先关闭所有插件
            shutdownAllPlugins();

            // 然后卸载所有插件
            auto pluginNames = std::vector<std::string>();
            for (const auto& pair : pluginInfos_) {
                pluginNames.push_back(pair.first);
            }

            for (const auto& name : pluginNames) {
                if (!unloadOnePlugin(name)) {
                    allUnloaded = false;
                }
            }

            // 最后卸载所有动态库
            libraryLoader_.unloadAllLibraries();

            recordLog("所有动态库卸载完成", LogLevel::INFO);
            return allUnloaded;
        } catch (const std::exception& e) {
            lastError_ = "卸载所有动态库时发生异常: " + std::string(e.what());
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }
    }

    /**
     * 获取插件通过名称
     * @param name 插件名称
     * @return 插件实例，未找到返回nullptr
     */
    IPlugin* getPlugin(const std::string& name) {
        auto it = pluginInfos_.find(name);
        return (it != pluginInfos_.end()) ? it->second.instance : nullptr;
    }

private:
    friend class singleton<SimplePluginManager>;

    // 日志级别枚举
    enum class LogLevel {
        INFO,
        WARNING,
        ERROR
    };

    // 插件函数指针结构
    struct PluginFunctions {
        create_plugin_func create_func;
        get_info_func info_func;
        set_singleton_accessor_func set_accessor_func;
        destroy_plugin_func destroy_func;

        PluginFunctions() : create_func(nullptr), info_func(nullptr),
                           set_accessor_func(nullptr), destroy_func(nullptr) {}
    };

    SimplePluginManager() : state_(PluginManagerState::UNINITIALIZED) {}

    ~SimplePluginManager() {
        try {
            unloadAllLibraries();
        } catch (...) {
            // 析构函数中不抛出异常
        }
    }

    /**
     * 加载插件函数指针
     * @param libPath 动态库路径
     * @param functions 函数指针结构
     * @return 加载成功返回true
     */
    bool loadPluginFunctions(const std::string& libPath, PluginFunctions& functions) {
        // 获取创建函数（必需）
        functions.create_func = libraryLoader_.getFunction<create_plugin_func>(libPath, "create_plugin");
        if (!functions.create_func) {
            lastError_ = "未找到create_plugin函数: " + libraryLoader_.getLastError();
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }

        // 获取信息函数（可选）
        functions.info_func = libraryLoader_.getFunction<get_info_func>(libPath, "get_plugin_info");

        // 获取单例访问器设置函数（可选）
        functions.set_accessor_func = libraryLoader_.getFunction<set_singleton_accessor_func>(libPath, "set_singleton_accessor");

        // 获取销毁函数（可选）
        functions.destroy_func = libraryLoader_.getFunction<destroy_plugin_func>(libPath, "destroy_plugin");

        return true;
    }

    /**
     * 初始化单个插件
     * @param pluginName 插件名称
     * @return 初始化成功返回true
     */
    bool initializeOnePlugin(const std::string& pluginName) {
        auto it = pluginInfos_.find(pluginName);
        if (it == pluginInfos_.end()) {
            lastError_ = "插件不存在: " + pluginName;
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }

        if (it->second.state != PluginState::LOADED) {
            lastError_ = "插件状态不正确，无法初始化: " + pluginName;
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }

        try {
            if (!it->second.instance->initialize()) {
                lastError_ = "插件初始化失败: " + pluginName;
                recordLog(lastError_, LogLevel::ERROR);
                it->second.state = PluginState::ERROR;
                return false;
            }

            it->second.state = PluginState::INITIALIZED;
            recordLog("插件 " + pluginName + " 初始化成功", LogLevel::INFO);
            return true;

        } catch (const std::exception& e) {
            lastError_ = "插件初始化时发生异常: " + std::string(e.what());
            recordLog(lastError_, LogLevel::ERROR);
            it->second.state = PluginState::ERROR;
            return false;
        }
    }

    /**
     * 启动单个插件
     * @param pluginName 插件名称
     * @return 启动成功返回true
     */
    bool startOnePlugin(const std::string& pluginName) {
        auto it = pluginInfos_.find(pluginName);
        if (it == pluginInfos_.end()) {
            lastError_ = "插件不存在: " + pluginName;
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }

        if (it->second.state != PluginState::INITIALIZED) {
            lastError_ = "插件状态不正确，无法启动: " + pluginName;
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }

        try {
            // 这里可以添加插件特定的启动逻辑
            it->second.state = PluginState::RUNNING;
            recordLog("插件 " + pluginName + " 启动成功", LogLevel::INFO);
            return true;

        } catch (const std::exception& e) {
            lastError_ = "插件启动时发生异常: " + std::string(e.what());
            recordLog(lastError_, LogLevel::ERROR);
            it->second.state = PluginState::ERROR;
            return false;
        }
    }

    /**
     * 停止单个插件
     * @param pluginName 插件名称
     * @return 停止成功返回true
     */
    bool stopOnePlugin(const std::string& pluginName) {
        auto it = pluginInfos_.find(pluginName);
        if (it == pluginInfos_.end()) {
            lastError_ = "插件不存在: " + pluginName;
            recordLog(lastError_, LogLevel::ERROR);
            return false;
        }

        if (it->second.state != PluginState::RUNNING) {
            return true; // 已经停止
        }

        try {
            if (it->second.instance) {
                it->second.instance->shutdown();
            }

            it->second.state = PluginState::STOPPED;
            recordLog("插件 " + pluginName + " 停止成功", LogLevel::INFO);
            return true;

        } catch (const std::exception& e) {
            lastError_ = "插件停止时发生异常: " + std::string(e.what());
            recordLog(lastError_, LogLevel::ERROR);
            it->second.state = PluginState::ERROR;
            return false;
        }
    }

    /**
     * 记录日志
     * @param message 日志消息
     * @param level 日志级别
     */
    void recordLog(const std::string& message, LogLevel level) const {
        switch (level) {
            case LogLevel::INFO:
                std::cout << "[INFO] " << message << std::endl;
                break;
            case LogLevel::WARNING:
                std::cout << "[WARNING] " << message << std::endl;
                break;
            case LogLevel::ERROR:
                std::cout << "[ERROR] " << message << std::endl;
                break;
        }
    }

    // 管理器状态
    PluginManagerState state_;

    // 最后的错误信息
    mutable std::string lastError_;

    // 动态库加载器
    zexuan::base::DynamicLibraryLoader libraryLoader_;

    // 插件信息映射
    std::map<std::string, PluginInfo> pluginInfos_;

    // 插件函数指针映射
    std::map<std::string, PluginFunctions> pluginFunctions_;
};

} // namespace plugin
} // namespace zexuan
